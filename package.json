{"name": "shorts", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "dev:jobs": "tsx watch src/jobs/index.ts", "build:jobs": "esbuild src/jobs/index.ts --bundle --outfile=dist/jobs/index.cjs --platform=node --format=cjs", "preview:jobs": "node dist/jobs/index.cjs", "drizzle:generate": "drizzle-kit generate", "drizzle:migrate": "drizzle-kit migrate", "drizzle:studio": "drizzle-kit studio", "seed": "tsx src/database/drizzle/seeds/index.ts", "seed:packages": "tsx src/database/drizzle/seeds/packages.ts", "lint": "eslint", "format": "prettier --write .", "translate": "tsx translate.ts", "prepare": "husky", "knip": "knip"}, "dependencies": {"@aws-sdk/client-s3": "^3.882.0", "@elevenlabs/elevenlabs-js": "^2.14.0", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@tanstack/react-table": "^8.21.3", "@tiptap/pm": "^3.4.1", "@tiptap/react": "^3.4.1", "@tiptap/starter-kit": "^3.4.1", "@traversable/zod": "^0.0.43", "@uidotdev/usehooks": "^2.4.1", "@universal-middleware/core": "^0.4.10", "axios": "^1.11.0", "better-auth": "^1.3.8", "bullmq": "^5.58.5", "callsite-record": "^4.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.2", "drizzle-orm": "^0.44.5", "es-toolkit": "^1.39.10", "fast-json-stable-stringify": "^2.1.0", "file-type": "^21.0.0", "handlebars": "^4.7.8", "html-to-text": "^9.0.5", "i18next": "^25.5.1", "ioredis": "^5.7.0", "js-cookie": "^3.0.5", "lucide-react": "^0.542.0", "marked": "^16.2.1", "next": "^15.5.2", "next-themes": "^0.4.6", "pexels": "^1.4.0", "pg": "^8.16.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-i18next": "^15.7.3", "react-icons": "^5.5.0", "sonner": "^2.0.7", "stripe": "^18.5.0", "tailwind-merge": "^3.3.1", "undici": "^7.15.0", "unsplash-js": "^7.0.19", "vaul": "^1.1.2", "zod": "^4.1.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.34.0", "@ianvs/prettier-plugin-sort-imports": "^4.7.0", "@tailwindcss/postcss": "^4", "@types/html-to-text": "^9.0.4", "@types/js-cookie": "^3.0.6", "@types/node": "^20.19.13", "@types/pg": "^8.15.5", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@vitalets/google-translate-api": "^9.2.1", "drizzle-kit": "^0.31.4", "esbuild": "^0.25.9", "eslint": "^9.34.0", "eslint-config-next": "15.5.2", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react": "^7.37.5", "husky": "^9.1.7", "inquirer": "^12.9.4", "knip": "^5.63.1", "lint-staged": "^16.1.6", "prettier": "^3.6.2", "prettier-plugin-multiline-arrays": "^4.0.3", "prettier-plugin-organize-imports": "^4.2.0", "tailwindcss": "^4", "ts-morph": "^26.0.0", "tsx": "^4.20.5", "tw-animate-css": "^1.3.8", "typescript": "^5.9.2", "typescript-eslint": "^8.42.0"}}